import 'package:flutter/material.dart';
import '../config/routes.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.help_center,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'How can we help you?',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Find answers to common questions or get in touch with our support team.',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Quick Actions
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildActionCard(
            context,
            'Contact Support',
            'Get in touch with our support team',
            Icons.contact_support,
            () => AppRoutes.navigateToContact(context),
          ),
          _buildActionCard(
            context,
            'Report an Issue',
            'Report bugs or technical problems',
            Icons.report_problem,
            () => AppRoutes.navigateToComplains(context),
          ),
          _buildActionCard(
            context,
            'Send Feedback',
            'Share your thoughts and suggestions',
            Icons.feedback,
            () => AppRoutes.navigateToUserFeedback(context),
          ),
          const SizedBox(height: 20),

          // FAQ Section
          Text(
            'Frequently Asked Questions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildFAQItem(
            context,
            'How do I change my location?',
            'Tap the search icon in the weather screen and enter your desired city name.',
          ),
          _buildFAQItem(
            context,
            'Why is the weather data not updating?',
            'Make sure you have an active internet connection and try refreshing the app.',
          ),
          _buildFAQItem(
            context,
            'How do I enable weather notifications?',
            'Go to Settings > Notifications and enable the weather alerts option.',
          ),
          _buildFAQItem(
            context,
            'Can I view weather for multiple cities?',
            'Yes, use the search feature to switch between different cities.',
          ),
          _buildFAQItem(
            context,
            'How accurate is the weather forecast?',
            'We use reliable weather data sources to provide accurate forecasts up to 5 days.',
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  Widget _buildFAQItem(BuildContext context, String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              answer,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
