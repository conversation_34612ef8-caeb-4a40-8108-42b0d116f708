{"configVersion": 2, "packages": [{"name": "archive", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "barcode", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/barcode-2.2.9", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bidi", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/bidi-2.0.13", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/checked_yaml-2.0.4", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "cli_util", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/connectivity_plus-5.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cross_file", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dbus", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "fake_async", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fixnum", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///D:/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_launcher_icons", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_launcher_icons-0.13.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter_lints", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_lints-3.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_local_notifications", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications-16.3.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_svg", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_svg-2.2.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_test", "rootUri": "file:///D:/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///D:/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "google_fonts", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/google_fonts-6.2.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "http", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/image-4.3.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "intl", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/intl-0.18.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "js", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/lints-3.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "lottie", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/lottie-2.7.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "matcher", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/mime-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_parsing", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pdf", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/pdf-3.11.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "pdf_widget_wrapper", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/pdf_widget_wrapper-1.0.4", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "printing", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/printing-5.14.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "provider", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "qr", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "share_plus", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/share_plus-7.2.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "share_plus_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/share_plus_platform_interface-3.4.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sky_engine", "rootUri": "file:///D:/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/synchronized-3.3.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "term_glyph", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timezone", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/timezone-0.9.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "typed_data", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher_linux", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/vector_graphics-1.1.19", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_graphics_codec", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/vector_graphics_compiler-1.1.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_math", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "win32", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/win32-5.14.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "xdg_directories", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///D:/FlutterCache/PubCache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "weather_forecast", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.0"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///D:/flutter", "flutterVersion": "3.32.2", "pubCache": "file:///D:/FlutterCache/PubCache"}