{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\weather forecast\\weather_forecast\\build\\.cxx\\Debug\\6y3q3j1w\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\weather forecast\\weather_forecast\\build\\.cxx\\Debug\\6y3q3j1w\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}