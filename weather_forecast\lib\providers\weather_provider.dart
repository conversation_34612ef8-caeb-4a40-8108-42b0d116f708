import 'package:flutter/foundation.dart';
import '../models/weather.dart';
import '../services/weather_service.dart';

class WeatherProvider with ChangeNotifier {
  Weather? _currentWeather;
  List<Weather> _forecast = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _selectedCity = 'London';

  Weather? get currentWeather => _currentWeather;
  List<Weather> get forecast => _forecast;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get selectedCity => _selectedCity;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  Future<void> fetchCurrentWeather([String? city]) async {
    _setLoading(true);
    _setError(null);

    try {
      final cityName = city ?? _selectedCity;
      final weather = await WeatherService.getCurrentWeather(cityName);
      _currentWeather = weather;
      _selectedCity = cityName;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to fetch weather: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<void> fetchWeatherForecast([String? city]) async {
    _setLoading(true);
    _setError(null);

    try {
      final cityName = city ?? _selectedCity;
      final forecastList = await WeatherService.getWeatherForecast(cityName);
      _forecast = forecastList;
      _selectedCity = cityName;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to fetch forecast: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<void> fetchAllWeatherData([String? city]) async {
    _setLoading(true);
    _setError(null);

    try {
      final cityName = city ?? _selectedCity;
      
      // Fetch both current weather and forecast
      final currentWeatherFuture = WeatherService.getCurrentWeather(cityName);
      final forecastFuture = WeatherService.getWeatherForecast(cityName);
      
      final results = await Future.wait([currentWeatherFuture, forecastFuture]);
      
      _currentWeather = results[0] as Weather;
      _forecast = results[1] as List<Weather>;
      _selectedCity = cityName;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to fetch weather data: ${e.toString()}');
      _setLoading(false);
    }
  }

  void setSelectedCity(String city) {
    _selectedCity = city;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearWeatherData() {
    _currentWeather = null;
    _forecast = [];
    _errorMessage = null;
    notifyListeners();
  }

  // Get weather for specific date
  Weather? getWeatherForDate(DateTime date) {
    try {
      return _forecast.firstWhere(
        (weather) => 
          weather.date.year == date.year &&
          weather.date.month == date.month &&
          weather.date.day == date.day,
      );
    } catch (e) {
      return null;
    }
  }

  // Get weather summary for report
  Map<String, dynamic> getWeatherSummary() {
    if (_currentWeather == null) return {};

    return {
      'city': _selectedCity,
      'current_temperature': _currentWeather!.temperature,
      'current_condition': _currentWeather!.condition,
      'current_humidity': _currentWeather!.humidity,
      'current_wind_speed': _currentWeather!.windSpeed,
      'forecast_count': _forecast.length,
      'min_temp_forecast': _forecast.isNotEmpty 
          ? _forecast.map((w) => w.temperature).reduce((a, b) => a < b ? a : b)
          : null,
      'max_temp_forecast': _forecast.isNotEmpty 
          ? _forecast.map((w) => w.temperature).reduce((a, b) => a > b ? a : b)
          : null,
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}
