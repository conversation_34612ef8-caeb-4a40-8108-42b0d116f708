import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/database_helper.dart';

class AuthProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _setError(null);

    try {
      final user = await DatabaseHelper.instance.loginUser(email, password);
      if (user != null) {
        _currentUser = user;
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Invalid email or password');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signUp(String name, String email, String password) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if user already exists
      final existingUser = await DatabaseHelper.instance.getUserByEmail(email);
      if (existingUser != null) {
        _setError('User with this email already exists');
        _setLoading(false);
        return false;
      }

      final user = User(
        name: name,
        email: email,
        password: password,
        createdAt: DateTime.now(),
      );

      final userId = await DatabaseHelper.instance.insertUser(user);
      if (userId > 0) {
        _currentUser = user.copyWith(id: userId);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to create account');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Sign up failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> resetPassword(String email, String newPassword) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await DatabaseHelper.instance.updateUserPassword(email, newPassword);
      if (success) {
        _setLoading(false);
        return true;
      } else {
        _setError('Email not found');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Password reset failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateProfile(String name, String email) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _setError(null);

    try {
      final success = await DatabaseHelper.instance.updateUser(
        _currentUser!.id!,
        name,
        email,
      );

      if (success) {
        _currentUser = _currentUser!.copyWith(name: name, email: email);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update profile');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  void logout() {
    _currentUser = null;
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
