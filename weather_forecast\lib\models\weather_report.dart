import 'weather.dart';

class WeatherReport {
  final int? id;
  final int userId;
  final String city;
  final DateTime date;
  final String summary;
  final double temperature;
  final double minTemp;
  final double maxTemp;
  final String condition;
  final int humidity;
  final double windSpeed;
  final double? pressure;
  final String? notes;
  final DateTime createdAt;

  WeatherReport({
    this.id,
    required this.userId,
    required this.city,
    required this.date,
    required this.summary,
    required this.temperature,
    required this.minTemp,
    required this.maxTemp,
    required this.condition,
    required this.humidity,
    required this.windSpeed,
    this.pressure,
    this.notes,
    required this.createdAt,
  });

  // Convert WeatherReport to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'city': city,
      'date': date.toIso8601String(),
      'summary': summary,
      'temperature': temperature,
      'min_temp': minTemp,
      'max_temp': maxTemp,
      'condition': condition,
      'humidity': humidity,
      'wind_speed': windSpeed,
      'pressure': pressure,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Create WeatherReport from Map (database result)
  factory WeatherReport.fromMap(Map<String, dynamic> map) {
    return WeatherReport(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      city: map['city'] ?? '',
      date: DateTime.parse(map['date']),
      summary: map['summary'] ?? '',
      temperature: map['temperature']?.toDouble() ?? 0.0,
      minTemp: map['min_temp']?.toDouble() ?? 0.0,
      maxTemp: map['max_temp']?.toDouble() ?? 0.0,
      condition: map['condition'] ?? '',
      humidity: map['humidity']?.toInt() ?? 0,
      windSpeed: map['wind_speed']?.toDouble() ?? 0.0,
      pressure: map['pressure']?.toDouble(),
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // Create WeatherReport from Weather object
  factory WeatherReport.fromWeather(Weather weather, int userId, {String? notes}) {
    return WeatherReport(
      userId: userId,
      city: weather.city,
      date: weather.date,
      summary: '${weather.condition} - ${weather.description}',
      temperature: weather.temperature,
      minTemp: weather.minTemp ?? weather.temperature,
      maxTemp: weather.maxTemp ?? weather.temperature,
      condition: weather.condition,
      humidity: weather.humidity,
      windSpeed: weather.windSpeed,
      pressure: weather.pressure,
      notes: notes,
      createdAt: DateTime.now(),
    );
  }

  // Create a copy of WeatherReport with updated fields
  WeatherReport copyWith({
    int? id,
    int? userId,
    String? city,
    DateTime? date,
    String? summary,
    double? temperature,
    double? minTemp,
    double? maxTemp,
    String? condition,
    int? humidity,
    double? windSpeed,
    double? pressure,
    String? notes,
    DateTime? createdAt,
  }) {
    return WeatherReport(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      city: city ?? this.city,
      date: date ?? this.date,
      summary: summary ?? this.summary,
      temperature: temperature ?? this.temperature,
      minTemp: minTemp ?? this.minTemp,
      maxTemp: maxTemp ?? this.maxTemp,
      condition: condition ?? this.condition,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      pressure: pressure ?? this.pressure,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Get temperature in Celsius (rounded)
  String get temperatureCelsius => '${temperature.round()}°C';

  // Get temperature range
  String get temperatureRange => '${minTemp.round()}°C - ${maxTemp.round()}°C';

  // Get wind speed in km/h
  String get windSpeedKmh => '${(windSpeed * 3.6).round()} km/h';

  // Get formatted date
  String get formattedDate => '${date.day}/${date.month}/${date.year}';

  // Get formatted creation date
  String get formattedCreatedAt => 
      '${createdAt.day}/${createdAt.month}/${createdAt.year} ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';

  // Get report title
  String get title => 'Weather Report - $city';

  // Get detailed summary for PDF
  String get detailedSummary => '''
Weather Report for $city
Date: $formattedDate
Generated: $formattedCreatedAt

Current Conditions:
- Temperature: $temperatureCelsius
- Temperature Range: $temperatureRange
- Condition: $condition
- Humidity: $humidity%
- Wind Speed: $windSpeedKmh
${pressure != null ? '- Pressure: ${pressure!.round()} hPa' : ''}

Summary: $summary

${notes != null ? 'Notes: $notes' : ''}
''';

  @override
  String toString() {
    return 'WeatherReport{id: $id, city: $city, date: $date, condition: $condition}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WeatherReport &&
        other.id == id &&
        other.userId == userId &&
        other.city == city &&
        other.date == date &&
        other.temperature == temperature;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        city.hashCode ^
        date.hashCode ^
        temperature.hashCode;
  }
}
