1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.weather_forecast"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:4:5-67
15-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Notification permissions -->
16-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:5:5-79
16-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:5:22-76
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:8:5-80
17-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:8:22-78
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:9:5-66
18-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:9:22-63
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:10:5-68
19-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:10:22-65
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions for database and reports -->
20-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:11:5-76
20-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:11:22-74
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:14:5-80
21-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:14:22-78
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:15:5-79
22-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:15:22-77
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:54:5-59:15
31        <intent>
31-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:55:9-58:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:56:13-72
32-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:56:21-70
33
34            <data android:mimeType="text/plain" />
34-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:57:13-50
34-->D:\weather forecast\weather_forecast\android\app\src\main\AndroidManifest.xml:57:19-48
35        </intent>
36    </queries>
37
38    <permission
38-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.example.weather_forecast.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.example.weather_forecast.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="true"
49        android:icon="@mipmap/ic_launcher"
50        android:label="weather_forecast" >
51        <activity
52            android:name="com.example.weather_forecast.MainActivity"
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
54            android:exported="true"
55            android:hardwareAccelerated="true"
56            android:launchMode="singleTop"
57            android:taskAffinity=""
58            android:theme="@style/LaunchTheme"
59            android:windowSoftInputMode="adjustResize" >
60
61            <!--
62                 Specifies an Android theme to apply to this Activity as soon as
63                 the Android process has started. This theme is visible to the user
64                 while the Flutter UI initializes. After that, this theme continues
65                 to determine the Window background behind the Flutter UI.
66            -->
67            <meta-data
68                android:name="io.flutter.embedding.android.NormalTheme"
69                android:resource="@style/NormalTheme" />
70
71            <intent-filter>
72                <action android:name="android.intent.action.MAIN" />
73
74                <category android:name="android.intent.category.LAUNCHER" />
75            </intent-filter>
76        </activity>
77        <!--
78             Don't delete the meta-data below.
79             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
80        -->
81        <meta-data
82            android:name="flutterEmbedding"
83            android:value="2" />
84
85        <uses-library
85-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
86            android:name="androidx.window.extensions"
86-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
87            android:required="false" />
87-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
88        <uses-library
88-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
89            android:name="androidx.window.sidecar"
89-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
90            android:required="false" />
90-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
91
92        <provider
92-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
93            android:name="androidx.startup.InitializationProvider"
93-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
94            android:authorities="com.example.weather_forecast.androidx-startup"
94-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
95            android:exported="false" >
95-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
96            <meta-data
96-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
97                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
97-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
98                android:value="androidx.startup" />
98-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
99            <meta-data
99-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
100                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
101                android:value="androidx.startup" />
101-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
102        </provider>
103
104        <receiver
104-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
105            android:name="androidx.profileinstaller.ProfileInstallReceiver"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
106            android:directBootAware="false"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
107            android:enabled="true"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
108            android:exported="true"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
109            android:permission="android.permission.DUMP" >
109-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
111                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
111-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
114                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
114-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
117                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
117-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
118            </intent-filter>
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
120                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
120-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
121            </intent-filter>
122        </receiver>
123    </application>
124
125</manifest>
