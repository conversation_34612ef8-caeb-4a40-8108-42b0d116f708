name: weather_forecast
description: A comprehensive weather forecast Flutter application with user authentication and local database.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0

  # State Management
  provider: ^6.1.1

  # Database
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  path: ^1.8.3

  # Network & API
  http: ^1.1.0

  # Date & Time
  intl: ^0.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  # assets:
  #   - assets/images/
  #   - assets/icons/


