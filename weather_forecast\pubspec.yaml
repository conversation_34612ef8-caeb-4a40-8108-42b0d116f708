name: weather_forecast
description: A comprehensive weather forecast Flutter application with user authentication and local database.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  lottie: ^2.7.0

  # State Management
  provider: ^6.1.1

  # Database
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  path: ^1.8.3

  # Network & API
  http: ^1.1.0
  connectivity_plus: ^5.0.2

  # Date & Time
  intl: ^0.18.1

  # Notifications
  flutter_local_notifications: ^16.3.2

  # Permissions
  permission_handler: ^11.2.0

  # File handling
  pdf: ^3.10.7
  printing: ^5.11.1
  share_plus: ^7.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  fonts:
    - family: CustomIcons
      fonts:
        - asset: assets/fonts/custom_icons.ttf
