import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/weather_provider.dart';
import '../models/weather.dart';

class ForecastList extends StatelessWidget {
  const ForecastList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WeatherProvider>(
      builder: (context, weatherProvider, child) {
        final forecast = weatherProvider.forecast;
        
        // Group forecast by date
        final groupedForecast = <String, List<Weather>>{};
        for (final weather in forecast) {
          final dateKey = DateFormat('yyyy-MM-dd').format(weather.date);
          if (!groupedForecast.containsKey(dateKey)) {
            groupedForecast[dateKey] = [];
          }
          groupedForecast[dateKey]!.add(weather);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: groupedForecast.length,
          itemBuilder: (context, index) {
            final dateKey = groupedForecast.keys.elementAt(index);
            final dayForecast = groupedForecast[dateKey]!;
            final date = DateTime.parse(dateKey);
            
            return _buildDayForecastCard(context, date, dayForecast);
          },
        );
      },
    );
  }

  Widget _buildDayForecastCard(
    BuildContext context,
    DateTime date,
    List<Weather> dayForecast,
  ) {
    final isToday = DateFormat('yyyy-MM-dd').format(date) == 
                   DateFormat('yyyy-MM-dd').format(DateTime.now());
    final isTomorrow = DateFormat('yyyy-MM-dd').format(date) == 
                      DateFormat('yyyy-MM-dd').format(DateTime.now().add(const Duration(days: 1)));

    String dayLabel;
    if (isToday) {
      dayLabel = 'Today';
    } else if (isTomorrow) {
      dayLabel = 'Tomorrow';
    } else {
      dayLabel = DateFormat('EEEE, MMM d').format(date);
    }

    // Get min and max temperatures for the day
    final temperatures = dayForecast.map((w) => w.temperature).toList();
    final minTemp = temperatures.reduce((a, b) => a < b ? a : b);
    final maxTemp = temperatures.reduce((a, b) => a > b ? a : b);

    // Get the most common condition for the day
    final conditions = dayForecast.map((w) => w.condition).toList();
    final mostCommonCondition = _getMostCommonCondition(conditions);
    final representativeWeather = dayForecast.firstWhere(
      (w) => w.condition == mostCommonCondition,
      orElse: () => dayForecast.first,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: representativeWeather.icon.isNotEmpty
              ? Image.network(
                  representativeWeather.iconUrl,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      _getWeatherIcon(representativeWeather.condition),
                      color: Theme.of(context).colorScheme.primary,
                    );
                  },
                )
              : Icon(
                  _getWeatherIcon(representativeWeather.condition),
                  color: Theme.of(context).colorScheme.primary,
                ),
        ),
        title: Text(
          dayLabel,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(mostCommonCondition),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${maxTemp.round()}°',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${minTemp.round()}°',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Hourly forecast for the day
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: dayForecast.length,
                    itemBuilder: (context, index) {
                      final weather = dayForecast[index];
                      return _buildHourlyForecastItem(context, weather);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHourlyForecastItem(BuildContext context, Weather weather) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            weather.formattedTime,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 8),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: weather.icon.isNotEmpty
                ? Image.network(
                    weather.iconUrl,
                    width: 30,
                    height: 30,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        _getWeatherIcon(weather.condition),
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      );
                    },
                  )
                : Icon(
                    _getWeatherIcon(weather.condition),
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
          ),
          const SizedBox(height: 8),
          Text(
            '${weather.temperature.round()}°',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            '${weather.humidity}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  String _getMostCommonCondition(List<String> conditions) {
    final conditionCounts = <String, int>{};
    for (final condition in conditions) {
      conditionCounts[condition] = (conditionCounts[condition] ?? 0) + 1;
    }
    
    return conditionCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return Icons.wb_sunny;
      case 'clouds':
        return Icons.cloud;
      case 'rain':
        return Icons.grain;
      case 'drizzle':
        return Icons.grain;
      case 'thunderstorm':
        return Icons.flash_on;
      case 'snow':
        return Icons.ac_unit;
      case 'mist':
      case 'fog':
        return Icons.foggy;
      default:
        return Icons.wb_sunny;
    }
  }
}
