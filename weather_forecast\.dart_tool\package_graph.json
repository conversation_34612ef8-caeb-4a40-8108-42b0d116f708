{"roots": ["weather_forecast"], "packages": [{"name": "weather_forecast", "version": "1.0.0+1", "dependencies": ["connectivity_plus", "cupertino_icons", "flutter", "flutter_local_notifications", "flutter_svg", "google_fonts", "http", "intl", "lottie", "path", "path_provider", "pdf", "permission_handler", "printing", "provider", "share_plus", "sqflite"], "devDependencies": ["flutter_launcher_icons", "flutter_lints", "flutter_test"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "share_plus", "version": "7.2.2", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "win32"]}, {"name": "intl", "version": "0.18.1", "dependencies": ["clock", "meta", "path"]}, {"name": "connectivity_plus", "version": "5.0.2", "dependencies": ["connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "js", "meta", "nm"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "lottie", "version": "2.7.0", "dependencies": ["archive", "flutter", "path", "vector_math"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "1.2.4", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "flutter_local_notifications", "version": "16.3.3", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "share_plus_platform_interface", "version": "3.4.0", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "mime", "version": "1.0.6", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "pdf", "version": "3.11.3", "dependencies": ["archive", "barcode", "bidi", "crypto", "image", "meta", "path_parsing", "vector_math", "xml"]}, {"name": "bidi", "version": "2.0.13", "dependencies": []}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "barcode", "version": "2.2.9", "dependencies": ["meta", "qr"]}, {"name": "qr", "version": "3.0.2", "dependencies": ["meta"]}, {"name": "printing", "version": "5.14.2", "dependencies": ["ffi", "flutter", "flutter_web_plugins", "http", "image", "meta", "pdf", "pdf_widget_wrapper", "plugin_platform_interface", "web"]}, {"name": "pdf_widget_wrapper", "version": "1.0.4", "dependencies": ["flutter", "pdf"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "flutter_svg", "version": "2.2.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "image", "version": "4.3.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}], "configVersion": 1}