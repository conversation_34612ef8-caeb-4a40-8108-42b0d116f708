import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/routes.dart';
import '../providers/auth_provider.dart';

class NavigationDrawer extends StatelessWidget {
  const NavigationDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // Header
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return UserAccountsDrawerHeader(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                ),
                accountName: Text(
                  authProvider.currentUser?.name ?? 'User',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                accountEmail: Text(
                  authProvider.currentUser?.email ?? '<EMAIL>',
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundColor: Colors.white,
                  child: Text(
                    authProvider.currentUser?.name.substring(0, 1).toUpperCase() ?? 'U',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            },
          ),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  onTap: () {
                    Navigator.pop(context);
                    // Already on dashboard
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.wb_sunny,
                  title: 'Weather Forecast',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToMain(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.assessment,
                  title: 'Weather Reports',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToReport(context);
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: Icons.person,
                  title: 'Account',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToAccount(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.settings,
                  title: 'Settings',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToSettings(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.feedback,
                  title: 'Send Feedback',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToUserFeedback(context);
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: Icons.help,
                  title: 'Help & Support',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToHelp(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.contact_support,
                  title: 'Contact Us',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToContact(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.privacy_tip,
                  title: 'Privacy Policy',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToPrivacyPolicy(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.report_problem,
                  title: 'Report Issue',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.navigateToComplains(context);
                  },
                ),
              ],
            ),
          ),

          // Logout
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.logout,
            title: 'Logout',
            onTap: () => _showLogoutDialog(context),
            textColor: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? Theme.of(context).colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: textColor ?? Theme.of(context).colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(); // Close drawer
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                authProvider.logout();
                AppRoutes.logout(context);
              },
              child: Text(
                'Logout',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
