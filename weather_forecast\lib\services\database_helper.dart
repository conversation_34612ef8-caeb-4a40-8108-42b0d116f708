import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../models/user.dart';
import '../models/feedback.dart';
import '../models/weather_report.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  static DatabaseHelper get instance => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'weather_forecast.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Feedbacks table
    await db.execute('''
      CREATE TABLE feedbacks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        message TEXT NOT NULL,
        category TEXT NOT NULL,
        rating INTEGER NOT NULL,
        timestamp TEXT NOT NULL,
        is_resolved INTEGER DEFAULT 0,
        response TEXT,
        response_date TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Weather reports table
    await db.execute('''
      CREATE TABLE weather_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        city TEXT NOT NULL,
        date TEXT NOT NULL,
        summary TEXT NOT NULL,
        temperature REAL NOT NULL,
        min_temp REAL NOT NULL,
        max_temp REAL NOT NULL,
        condition TEXT NOT NULL,
        humidity INTEGER NOT NULL,
        wind_speed REAL NOT NULL,
        pressure REAL,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Insert default admin user for testing
    await db.insert('users', {
      'name': 'Admin User',
      'email': '<EMAIL>',
      'password': 'admin123',
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  // User operations
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toMap());
  }

  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> loginUser(String email, String password) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ? AND password = ?',
      whereArgs: [email, password],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<bool> updateUser(int id, String name, String email) async {
    final db = await database;
    final result = await db.update(
      'users',
      {
        'name': name,
        'email': email,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
    return result > 0;
  }

  Future<bool> updateUserPassword(String email, String newPassword) async {
    final db = await database;
    final result = await db.update(
      'users',
      {
        'password': newPassword,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'email = ?',
      whereArgs: [email],
    );
    return result > 0;
  }

  // Feedback operations
  Future<int> insertFeedback(UserFeedback feedback) async {
    final db = await database;
    return await db.insert('feedbacks', feedback.toMap());
  }

  Future<List<UserFeedback>> getFeedbacksByUser(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'feedbacks',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return UserFeedback.fromMap(maps[i]);
    });
  }

  Future<List<UserFeedback>> getAllFeedbacks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'feedbacks',
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return UserFeedback.fromMap(maps[i]);
    });
  }

  // Weather report operations
  Future<int> insertWeatherReport(WeatherReport report) async {
    final db = await database;
    return await db.insert('weather_reports', report.toMap());
  }

  Future<List<WeatherReport>> getWeatherReportsByUser(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'weather_reports',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return WeatherReport.fromMap(maps[i]);
    });
  }

  Future<List<WeatherReport>> getAllWeatherReports() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'weather_reports',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return WeatherReport.fromMap(maps[i]);
    });
  }

  Future<bool> deleteWeatherReport(int id) async {
    final db = await database;
    final result = await db.delete(
      'weather_reports',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result > 0;
  }

  // Database utility methods
  Future<void> close() async {
    final db = await database;
    db.close();
  }

  Future<void> deleteDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'weather_forecast.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
