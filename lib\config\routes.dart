import 'package:flutter/material.dart';

// Import all screens
import '../screens/splash_screen.dart';
import '../screens/login_screen.dart';
import '../screens/signup_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/main_screen.dart';
import '../screens/reset_password_screen.dart';
import '../screens/user_feedback_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/account_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/complains_screen.dart';
import '../screens/help_screen.dart';
import '../screens/contact_screen.dart';
import '../screens/report_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String dashboard = '/dashboard';
  static const String main = '/main';
  static const String resetPassword = '/reset-password';
  static const String userFeedback = '/user-feedback';
  static const String settings = '/settings';
  static const String account = '/account';
  static const String privacyPolicy = '/privacy-policy';
  static const String complains = '/complains';
  static const String help = '/help';
  static const String contact = '/contact';
  static const String report = '/report';

  // Route map
  static Map<String, WidgetBuilder> get routes {
    return {
      splash: (context) => const SplashScreen(),
      login: (context) => const LoginScreen(),
      signup: (context) => const SignUpScreen(),
      dashboard: (context) => const DashboardScreen(),
      main: (context) => const MainScreen(),
      resetPassword: (context) => const ResetPasswordScreen(),
      userFeedback: (context) => const UserFeedbackScreen(),
      settings: (context) => const SettingsScreen(),
      account: (context) => const AccountScreen(),
      privacyPolicy: (context) => const PrivacyPolicyScreen(),
      complains: (context) => const ComplainsScreen(),
      help: (context) => const HelpScreen(),
      contact: (context) => const ContactScreen(),
      report: (context) => const ReportScreen(),
    };
  }

  // Navigation helper methods
  static void navigateToLogin(BuildContext context) {
    Navigator.pushReplacementNamed(context, login);
  }

  static void navigateToDashboard(BuildContext context) {
    Navigator.pushReplacementNamed(context, dashboard);
  }

  static void navigateToMain(BuildContext context) {
    Navigator.pushNamed(context, main);
  }

  static void navigateToSignUp(BuildContext context) {
    Navigator.pushNamed(context, signup);
  }

  static void navigateToResetPassword(BuildContext context) {
    Navigator.pushNamed(context, resetPassword);
  }

  static void navigateToUserFeedback(BuildContext context) {
    Navigator.pushNamed(context, userFeedback);
  }

  static void navigateToSettings(BuildContext context) {
    Navigator.pushNamed(context, settings);
  }

  static void navigateToAccount(BuildContext context) {
    Navigator.pushNamed(context, account);
  }

  static void navigateToPrivacyPolicy(BuildContext context) {
    Navigator.pushNamed(context, privacyPolicy);
  }

  static void navigateToComplains(BuildContext context) {
    Navigator.pushNamed(context, complains);
  }

  static void navigateToHelp(BuildContext context) {
    Navigator.pushNamed(context, help);
  }

  static void navigateToContact(BuildContext context) {
    Navigator.pushNamed(context, contact);
  }

  static void navigateToReport(BuildContext context) {
    Navigator.pushNamed(context, report);
  }

  static void logout(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(context, login, (route) => false);
  }
}
