import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/weather.dart';

class WeatherService {
  // Replace with your actual OpenWeatherMap API key
  // Get your free API key from: https://openweathermap.org/api
  static const String _apiKey = 'YOUR_API_KEY_HERE';
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';

  // For demo purposes, we'll use mock data
  // Set this to false and add your API key to use real weather data
  static const bool _useMockData = true;

  static Future<Weather> getCurrentWeather(String cityName) async {
    if (_useMockData) {
      return _getMockCurrentWeather(cityName);
    }

    try {
      final url = '$_baseUrl/weather?q=$cityName&appid=$_apiKey&units=metric';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Weather.fromJson(data, cityName);
      } else {
        throw Exception('Failed to load weather data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weather: $e');
    }
  }

  static Future<List<Weather>> getWeatherForecast(String cityName) async {
    if (_useMockData) {
      return _getMockForecast(cityName);
    }

    try {
      final url = '$_baseUrl/forecast?q=$cityName&appid=$_apiKey&units=metric';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> forecastList = data['list'];
        
        return forecastList.map((item) => Weather.fromForecastJson(item, cityName)).toList();
      } else {
        throw Exception('Failed to load forecast data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching forecast: $e');
    }
  }

  // Mock data for demo purposes
  static Weather _getMockCurrentWeather(String cityName) {
    return Weather(
      city: cityName,
      temperature: 22.5,
      feelsLike: 24.0,
      humidity: 65,
      windSpeed: 3.2,
      condition: 'Clear',
      description: 'clear sky',
      icon: '01d',
      date: DateTime.now(),
      minTemp: 18.0,
      maxTemp: 26.0,
      pressure: 1013.0,
      visibility: 10000.0,
    );
  }

  static List<Weather> _getMockForecast(String cityName) {
    final now = DateTime.now();
    final forecast = <Weather>[];

    // Generate 5 days of forecast data (8 entries per day, every 3 hours)
    for (int day = 0; day < 5; day++) {
      for (int hour = 0; hour < 8; hour++) {
        final date = now.add(Duration(days: day, hours: hour * 3));
        final baseTemp = 20.0 + (day * 2) + (hour * 0.5);
        final conditions = ['Clear', 'Clouds', 'Rain', 'Drizzle'];
        final icons = ['01d', '02d', '10d', '09d'];
        final conditionIndex = (day + hour) % conditions.length;

        forecast.add(Weather(
          city: cityName,
          temperature: baseTemp + (hour % 2 == 0 ? 2 : -1),
          feelsLike: baseTemp + 1.5,
          humidity: 60 + (hour * 2),
          windSpeed: 2.0 + (hour * 0.3),
          condition: conditions[conditionIndex],
          description: conditions[conditionIndex].toLowerCase(),
          icon: icons[conditionIndex],
          date: date,
          minTemp: baseTemp - 3,
          maxTemp: baseTemp + 4,
          pressure: 1010.0 + (hour * 0.5),
        ));
      }
    }

    return forecast;
  }

  // Helper method to get weather icon based on condition
  static String getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return '01d';
      case 'clouds':
        return '02d';
      case 'rain':
        return '10d';
      case 'drizzle':
        return '09d';
      case 'thunderstorm':
        return '11d';
      case 'snow':
        return '13d';
      case 'mist':
      case 'fog':
        return '50d';
      default:
        return '01d';
    }
  }

  // Method to validate API key (for when real API is used)
  static bool isApiKeyValid() {
    return _apiKey != 'YOUR_API_KEY_HERE' && _apiKey.isNotEmpty;
  }

  // Method to set up real API (for future use)
  static void setupRealApi() {
    // This would be called when user provides a valid API key
    // For now, we'll continue using mock data
  }
}
