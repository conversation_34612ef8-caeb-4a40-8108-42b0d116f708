{"inputs": ["D:\\weather forecast\\weather_forecast\\.dart_tool\\flutter_build\\f1f5b0b370b46cf332474a6e5140d986\\app.dill", "D:\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\weather forecast\\weather_forecast\\pubspec.yaml", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\weather forecast\\weather_forecast\\.dart_tool\\flutter_build\\f1f5b0b370b46cf332474a6e5140d986\\native_assets.json", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_lints-3.0.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\google_fonts-6.2.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\intl-0.18.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\lints-3.0.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "D:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\flutter\\packages\\flutter\\LICENSE"], "outputs": ["D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\weather forecast\\weather_forecast\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}