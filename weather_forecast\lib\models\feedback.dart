class UserFeedback {
  final int? id;
  final int userId;
  final String message;
  final String category;
  final int rating;
  final DateTime timestamp;
  final bool isResolved;
  final String? response;
  final DateTime? responseDate;

  UserFeedback({
    this.id,
    required this.userId,
    required this.message,
    required this.category,
    required this.rating,
    required this.timestamp,
    this.isResolved = false,
    this.response,
    this.responseDate,
  });

  // Convert UserFeedback to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'message': message,
      'category': category,
      'rating': rating,
      'timestamp': timestamp.toIso8601String(),
      'is_resolved': isResolved ? 1 : 0,
      'response': response,
      'response_date': responseDate?.toIso8601String(),
    };
  }

  // Create UserFeedback from Map (database result)
  factory UserFeedback.fromMap(Map<String, dynamic> map) {
    return UserFeedback(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      message: map['message'] ?? '',
      category: map['category'] ?? '',
      rating: map['rating']?.toInt() ?? 0,
      timestamp: DateTime.parse(map['timestamp']),
      isResolved: (map['is_resolved'] ?? 0) == 1,
      response: map['response'],
      responseDate: map['response_date'] != null 
          ? DateTime.parse(map['response_date']) 
          : null,
    );
  }

  // Create a copy of UserFeedback with updated fields
  UserFeedback copyWith({
    int? id,
    int? userId,
    String? message,
    String? category,
    int? rating,
    DateTime? timestamp,
    bool? isResolved,
    String? response,
    DateTime? responseDate,
  }) {
    return UserFeedback(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      message: message ?? this.message,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      timestamp: timestamp ?? this.timestamp,
      isResolved: isResolved ?? this.isResolved,
      response: response ?? this.response,
      responseDate: responseDate ?? this.responseDate,
    );
  }

  // Get formatted timestamp
  String get formattedTimestamp => 
      '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';

  // Get rating stars
  String get ratingStars => '★' * rating + '☆' * (5 - rating);

  // Get status text
  String get statusText => isResolved ? 'Resolved' : 'Pending';

  @override
  String toString() {
    return 'UserFeedback{id: $id, userId: $userId, category: $category, rating: $rating, isResolved: $isResolved}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserFeedback &&
        other.id == id &&
        other.userId == userId &&
        other.message == message &&
        other.category == category &&
        other.rating == rating &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        message.hashCode ^
        category.hashCode ^
        rating.hashCode ^
        timestamp.hashCode;
  }
}

// Feedback categories
class FeedbackCategory {
  static const String general = 'General';
  static const String bugReport = 'Bug Report';
  static const String featureRequest = 'Feature Request';
  static const String weatherAccuracy = 'Weather Accuracy';
  static const String userInterface = 'User Interface';
  static const String performance = 'Performance';

  static List<String> get all => [
    general,
    bugReport,
    featureRequest,
    weatherAccuracy,
    userInterface,
    performance,
  ];
}
