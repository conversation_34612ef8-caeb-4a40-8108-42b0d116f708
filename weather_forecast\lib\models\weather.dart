class Weather {
  final int? id;
  final String city;
  final double temperature;
  final double feelsLike;
  final int humidity;
  final double windSpeed;
  final String condition;
  final String description;
  final String icon;
  final DateTime date;
  final double? minTemp;
  final double? maxTemp;
  final double? pressure;
  final double? visibility;
  final int? uvIndex;

  Weather({
    this.id,
    required this.city,
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.windSpeed,
    required this.condition,
    required this.description,
    required this.icon,
    required this.date,
    this.minTemp,
    this.maxTemp,
    this.pressure,
    this.visibility,
    this.uvIndex,
  });

  // Convert Weather to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'city': city,
      'temperature': temperature,
      'feels_like': feelsLike,
      'humidity': humidity,
      'wind_speed': windSpeed,
      'condition': condition,
      'description': description,
      'icon': icon,
      'date': date.toIso8601String(),
      'min_temp': minTemp,
      'max_temp': maxTemp,
      'pressure': pressure,
      'visibility': visibility,
      'uv_index': uvIndex,
    };
  }

  // Create Weather from Map (database result)
  factory Weather.fromMap(Map<String, dynamic> map) {
    return Weather(
      id: map['id']?.toInt(),
      city: map['city'] ?? '',
      temperature: map['temperature']?.toDouble() ?? 0.0,
      feelsLike: map['feels_like']?.toDouble() ?? 0.0,
      humidity: map['humidity']?.toInt() ?? 0,
      windSpeed: map['wind_speed']?.toDouble() ?? 0.0,
      condition: map['condition'] ?? '',
      description: map['description'] ?? '',
      icon: map['icon'] ?? '',
      date: DateTime.parse(map['date']),
      minTemp: map['min_temp']?.toDouble(),
      maxTemp: map['max_temp']?.toDouble(),
      pressure: map['pressure']?.toDouble(),
      visibility: map['visibility']?.toDouble(),
      uvIndex: map['uv_index']?.toInt(),
    );
  }

  // Create Weather from OpenWeatherMap API response
  factory Weather.fromJson(Map<String, dynamic> json, String cityName) {
    final main = json['main'];
    final weather = json['weather'][0];
    final wind = json['wind'] ?? {};
    
    return Weather(
      city: cityName,
      temperature: main['temp']?.toDouble() ?? 0.0,
      feelsLike: main['feels_like']?.toDouble() ?? 0.0,
      humidity: main['humidity']?.toInt() ?? 0,
      windSpeed: wind['speed']?.toDouble() ?? 0.0,
      condition: weather['main'] ?? '',
      description: weather['description'] ?? '',
      icon: weather['icon'] ?? '',
      date: DateTime.now(),
      minTemp: main['temp_min']?.toDouble(),
      maxTemp: main['temp_max']?.toDouble(),
      pressure: main['pressure']?.toDouble(),
      visibility: json['visibility']?.toDouble(),
    );
  }

  // Create Weather from forecast API response
  factory Weather.fromForecastJson(Map<String, dynamic> json, String cityName) {
    final main = json['main'];
    final weather = json['weather'][0];
    final wind = json['wind'] ?? {};
    
    return Weather(
      city: cityName,
      temperature: main['temp']?.toDouble() ?? 0.0,
      feelsLike: main['feels_like']?.toDouble() ?? 0.0,
      humidity: main['humidity']?.toInt() ?? 0,
      windSpeed: wind['speed']?.toDouble() ?? 0.0,
      condition: weather['main'] ?? '',
      description: weather['description'] ?? '',
      icon: weather['icon'] ?? '',
      date: DateTime.parse(json['dt_txt']),
      minTemp: main['temp_min']?.toDouble(),
      maxTemp: main['temp_max']?.toDouble(),
      pressure: main['pressure']?.toDouble(),
    );
  }

  // Create a copy of Weather with updated fields
  Weather copyWith({
    int? id,
    String? city,
    double? temperature,
    double? feelsLike,
    int? humidity,
    double? windSpeed,
    String? condition,
    String? description,
    String? icon,
    DateTime? date,
    double? minTemp,
    double? maxTemp,
    double? pressure,
    double? visibility,
    int? uvIndex,
  }) {
    return Weather(
      id: id ?? this.id,
      city: city ?? this.city,
      temperature: temperature ?? this.temperature,
      feelsLike: feelsLike ?? this.feelsLike,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      date: date ?? this.date,
      minTemp: minTemp ?? this.minTemp,
      maxTemp: maxTemp ?? this.maxTemp,
      pressure: pressure ?? this.pressure,
      visibility: visibility ?? this.visibility,
      uvIndex: uvIndex ?? this.uvIndex,
    );
  }

  // Get weather icon URL
  String get iconUrl => 'https://openweathermap.org/img/wn/$<EMAIL>';

  // Get temperature in Celsius (rounded)
  String get temperatureCelsius => '${temperature.round()}°C';

  // Get temperature in Fahrenheit
  String get temperatureFahrenheit => '${(temperature * 9/5 + 32).round()}°F';

  // Get wind speed in km/h
  String get windSpeedKmh => '${(windSpeed * 3.6).round()} km/h';

  // Get formatted date
  String get formattedDate => '${date.day}/${date.month}/${date.year}';

  // Get formatted time
  String get formattedTime => '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';

  @override
  String toString() {
    return 'Weather{city: $city, temperature: $temperature, condition: $condition, date: $date}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Weather &&
        other.id == id &&
        other.city == city &&
        other.temperature == temperature &&
        other.condition == condition &&
        other.date == date;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        city.hashCode ^
        temperature.hashCode ^
        condition.hashCode ^
        date.hashCode;
  }
}
