import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/weather_provider.dart';
import '../widgets/weather_card.dart';
import '../widgets/forecast_list.dart';
import '../widgets/city_search.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadWeatherData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadWeatherData() {
    final weatherProvider = Provider.of<WeatherProvider>(context, listen: false);
    weatherProvider.fetchAllWeatherData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Weather Forecast'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showCitySearch(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadWeatherData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.wb_sunny),
              text: 'Current',
            ),
            Tab(
              icon: Icon(Icons.calendar_today),
              text: 'Forecast',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Current Weather Tab
          const CurrentWeatherTab(),
          // Forecast Tab
          const ForecastTab(),
        ],
      ),
    );
  }

  void _showCitySearch(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const CitySearch(),
    );
  }
}

class CurrentWeatherTab extends StatelessWidget {
  const CurrentWeatherTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Weather Card
          const WeatherCard(),
          const SizedBox(height: 20),

          // Additional Weather Info
          Consumer<WeatherProvider>(
            builder: (context, weatherProvider, child) {
              final weather = weatherProvider.currentWeather;
              if (weather == null) return const SizedBox.shrink();

              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Weather Details',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildDetailRow(
                        context,
                        'Temperature Range',
                        weather.minTemp != null && weather.maxTemp != null
                            ? '${weather.minTemp!.round()}°C - ${weather.maxTemp!.round()}°C'
                            : 'N/A',
                        Icons.thermostat,
                      ),
                      _buildDetailRow(
                        context,
                        'Feels Like',
                        '${weather.feelsLike.round()}°C',
                        Icons.thermostat_outlined,
                      ),
                      _buildDetailRow(
                        context,
                        'Humidity',
                        '${weather.humidity}%',
                        Icons.water_drop,
                      ),
                      _buildDetailRow(
                        context,
                        'Wind Speed',
                        weather.windSpeedKmh,
                        Icons.air,
                      ),
                      if (weather.pressure != null)
                        _buildDetailRow(
                          context,
                          'Pressure',
                          '${weather.pressure!.round()} hPa',
                          Icons.speed,
                        ),
                      if (weather.visibility != null)
                        _buildDetailRow(
                          context,
                          'Visibility',
                          '${(weather.visibility! / 1000).round()} km',
                          Icons.visibility,
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class ForecastTab extends StatelessWidget {
  const ForecastTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WeatherProvider>(
      builder: (context, weatherProvider, child) {
        if (weatherProvider.isLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading forecast...'),
              ],
            ),
          );
        }

        if (weatherProvider.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load forecast',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  weatherProvider.errorMessage!,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => weatherProvider.fetchWeatherForecast(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (weatherProvider.forecast.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No forecast data available',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => weatherProvider.fetchWeatherForecast(),
                  child: const Text('Load Forecast'),
                ),
              ],
            ),
          );
        }

        return const ForecastList();
      },
    );
  }
}
